Index: app.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import schedule\nimport os\nimport json\nimport asyncio\nfrom datetime import datetime, timedelta\nimport random\nimport uuid\nfrom collections import Counter\nfrom bullmq import Queue, Worker, Job\nfrom bullmq.types import QueueBaseOptions, WorkerOptions\nfrom utils.mgdatabase import (\n    MongoDBManager\n)\nfrom algo.bandit_algo import create_user_context, run_bandit\nfrom utils.utils import find_all_placeholders, generate_and_fill_notification\nfrom utils.common import PLACEHOLDER_DATA_EN\nfrom dotenv import load_dotenv\nload_dotenv()\n\nmgdb = MongoDBManager()\nconnection_opts = {\n    \"host\": os.environ.get('REDIS_HOST', 'redis-19926.c1.asia-northeast1-1.gce.cloud.redislabs.com'),\n    \"port\": os.environ.get('REDIS_PORT', 19926),\n    \"username\": os.environ.get('REDIS_USERNAME', 'default'),\n    \"password\": os.environ.get('REDIS_PASSWORD', 'X4vtYIZ5UE7CIACfqCxtm67kK1GX4S6J')\n}\nasync def set_queue_notification(data_job):\n    try:\n        queue_opts = QueueBaseOptions(connection=connection_opts)\n        queue = Queue(\"foxy-ai-service-notification\", queue_opts)\n        job_ids = []\n        for data in data_job:\n            id_job = str(uuid.uuid4())\n            datetime_recommender = data[\"recommended_time\"]\n            delay_seconds = abs((datetime.now() - datetime_recommender).total_seconds()) / 60\n            if delay_seconds <= 0:\n                delay_seconds = 5 * 60\n            delay_ms = delay_seconds * 1000\n            \n            job_data = {\n                'notification_id': id_job,\n                'user_id': data[\"user_id\"],\n                'title': data[\"title\"],\n                'content': data[\"content\"],\n                \"category\": data[\"category\"],\n                'notification_type': data[\"notification_type\"],\n                \"template_id\": data[\"template_id\"],\n                \"confidence_score\": data[\"confidence_score\"],\n                \"expected_engagement\": data[\"expected_engagement\"],\n                'delay_seconds': delay_seconds\n            }\n            job_options = {\n                \"removeOnComplete\": 10000,\n                \"removeOnFail\": 5000,\n                \"attempts\": 3,\n                \"backoff\": {\n                    \"type\": 'exponential',\n                    \"delay\": 1000,\n                },\n            }\n            if delay_ms > 0:\n                job_options['delay'] = delay_ms\n                job_options[\"backoff\"][\"delay\"] = delay_ms\n            \n            job = await queue.add(f\"ai-service-notification-{id_job}\", job_data, job_options)\n            job_ids.append(job.id)\n        return True\n        \n    except Exception as e:\n        print(f\"❌ Error during test: {e}\")\n        import traceback\n        traceback.print_exc()\n        return False\n\nasync def create_notification_recommender_system():\n    \"\"\"\n    Create a notification recommendation system\n    \"\"\"\n    print(\"\uD83D\uDE80 Starting notification recommendation system...\")\n\n    # Get all users from database (these are user profiles, not notification contexts)\n    all_users = mgdb.get_all_user()\n    print(f\"\uD83D\uDCCA Found {len(all_users)} user profiles in database\")\n    user_contexts = []\n    user_notifaction = {}\n    if all_users:\n        for user in all_users:\n            if user.get('id', '').startswith('partnr'):\n                user_profile = user\n                data_message = mgdb.get_history_chat_by_user_today(user['id'])\n                # user_contexts.append(create_sample_user_context(user))\n                if len(data_message) > 0:\n                    user_notifaction[user['id']] = {\n                        \"name\": user.get(\"name\", \"unknown\")\n                    }\n                    if \"interests\" in user_profile and type(user_profile['interests']) == list:\n                        user_profile['interests_target'] = \",\".join(user_profile.get('interests', []))\n                    else:\n                        user_profile['interests_target'] = \"nsfw\"\n\n                    # Lấy unique character_ids với số lượng, sort từ cao đến thấp\n                    # Đếm số lần xuất hiện của mỗi character_id\n                    character_count = Counter(obj['character_id'] for obj in data_message)\n\n                    # Sort từ cao đến thấp theo số lượng\n                    sorted_character_ids = sorted(character_count.items(), key=lambda x: x[1], reverse=True)\n                    user_profile['num_chat_characters'] = len(sorted_character_ids)\n                    user_profile['sorted_character_ids'] = sorted_character_ids\n                    unique_character_ids = [character_id for character_id, count in sorted_character_ids]\n                    user_profile['unique_character_ids'] = unique_character_ids\n                    user_profile[\"messages_per_day\"] = len(data_message)\n                    user_profile[\"notification_click_rate\"] = random.uniform(0.1, 0.5)\n                    # last online:\n                    last_message_timestamp = max(obj['created_at'] for obj in data_message)\n                    last_message_datetime = datetime.fromtimestamp(last_message_timestamp)\n                    user_profile[\"last_online_time\"] = last_message_datetime\n                    user_profile[\"day_of_week\"] = datetime.now().weekday()\n                    user_profile[\"hour_of_day\"] = datetime.now().hour\n                    user_profile[\"cumulative_clicks\"] = random.randint(5, 100)\n                    user_profile[\"online_time_periods\"] = [(datetime.now() - timedelta(hours=random.randint(2,5)), datetime.now() - timedelta(hours=random.randint(0,1)))],\n                    user_profile[\"current_mood\"] = random.choice(['happy', 'lonely', 'neutral']),\n                    user_profile[\"preferred_character_types\"] = ['friendly', 'romantic', \"nsfw\"]\n                    user_profile[\"age\"] = random.randint(18, 30)\n                    user_contexts.append(create_user_context(user_profile))\n\n    print(\"✨ Notification recommendation system initialization complete!\")\n    all_notifications = mgdb.get_all_notification()\n    data_notification = run_bandit(user_contexts, all_notifications)\n    for key_user in list(user_notifaction.keys()):\n        data_value = data_notification[key_user]\n        data_fill_notification = []\n        if len(data_value[\"rec\"]) > 0:\n            name_user = (lambda name: name if len(name) > 1 else \"daddy\")(user_notifaction[key_user][\"name\"])\n            if len(data_value[\"character_list\"]) > 0:\n                object_character = mgdb.get_character_information(data_value[\"character_list\"][0])\n                character_name = object_character[\"name\"]\n                character_country = object_character[\"country\"]\n            else:\n                character_name = \"Mika\"\n                character_country = \"USA\"\n            for rec in data_value[\"rec\"]:\n                unique_placeholder_keys_from_file = set()\n                content_notification = [{\n                    \"title\": rec[\"title\"],\n                    \"content\": rec[\"content\"]\n                }]\n                find_all_placeholders(content_notification, unique_placeholder_keys_from_file)\n                filled_notifications = generate_and_fill_notification(\n                    PLACEHOLDER_DATA_EN,\n                    content_notification,\n                    list(unique_placeholder_keys_from_file),\n                    name_user,\n                    character_name,\n                    character_country\n\n                )\n                rec[\"title\"] = filled_notifications[0][\"title\"]\n                rec[\"content\"] = filled_notifications[0][\"content\"]\n                data_fill_notification.append(rec)\n        await set_queue_notification(data_fill_notification)\n        \nif __name__ == \"__main__\":\n    asyncio.run(create_notification_recommender_system())\n
===================================================================
diff --git a/app.py b/app.py
--- a/app.py	(revision ed25abb00692ba4ffd1ed4e1e95806a782df3107)
+++ b/app.py	(date 1748510618142)
@@ -19,10 +19,10 @@
 
 mgdb = MongoDBManager()
 connection_opts = {
-    "host": os.environ.get('REDIS_HOST', 'redis-19926.c1.asia-northeast1-1.gce.cloud.redislabs.com'),
+    "host": os.environ.get('REDIS_HOST', ''),
     "port": os.environ.get('REDIS_PORT', 19926),
-    "username": os.environ.get('REDIS_USERNAME', 'default'),
-    "password": os.environ.get('REDIS_PASSWORD', 'X4vtYIZ5UE7CIACfqCxtm67kK1GX4S6J')
+    "username": os.environ.get('REDIS_USERNAME', ''),
+    "password": os.environ.get('REDIS_PASSWORD', '')
 }
 async def set_queue_notification(data_job):
     try:
