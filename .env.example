# redis config
REDIS_HOST=
REDIS_PORT=19926
REDIS_USERNAME=default
REDIS_PASSWORD=your-redis-password

# MongoDB Configuration
MONGODB_URI=mongodb://*************:27117/admin
MONGODB_DATABASE=partnr-chat-mgdb
MONGODB_USERNAME=admin
MONGODB_PASSWORD=Mongo

# MongoDB Collections
COLLECTION_USERS=users
COLLECTION_NOTIFICATIONS=notifications
COLLECTION_CHAT_HISTORY=chat_history

# Application Settings
DEBUG=True
LOG_LEVEL=INFO

