# Foxy AI Service - Intelligent Notification Recommendation System

A sophisticated AI-powered notification recommendation system that uses multi-armed bandit algorithms to deliver personalized notifications to users based on their behavior, preferences, and engagement patterns.

## 🚀 Features

- **Multi-Armed Bandit Algorithm**: Uses contextual bandits with LinUCB for intelligent notification selection
- **Real-time Personalization**: Adapts recommendations based on user behavior and preferences
- **MongoDB Integration**: Stores user profiles, chat history, and notification templates
- **Redis Queue Management**: Handles notification scheduling and delivery via BullMQ
- **NSFW Content Filtering**: Respects user tolerance levels for adult content
- **Performance Analytics**: Tracks click-through rates and engagement metrics
- **Placeholder System**: Dynamic content generation with user-specific variables

## 🏗️ Architecture

The system consists of several key components:

- **Bandit Algorithm** (`algo/bandit_algo.py`): Core recommendation engine
- **Database Manager** (`utils/mgdatabase.py`): MongoDB operations
- **Notification Queue** (`utils/notification_queue.py`): Redis queue management
- **Content Generation** (`utils/utils.py`): Dynamic content creation
- **Main Application** (`app.py`): Orchestrates the recommendation process

## 📋 Prerequisites

- Python 3.8+
- MongoDB
- Redis
- Required Python packages (see requirements.txt)

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd foxy-ai-service-rs
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Environment Setup**
   Create a `.env` file in the root directory:
   ```env
   # Redis Configuration
   REDIS_HOST=your-redis-host
   REDIS_PORT=19926
   REDIS_USERNAME=default
   REDIS_PASSWORD=your-redis-password

   # MongoDB Configuration
   MONGODB_URI=mongodb://localhost:27017/
   MONGODB_DATABASE=foxy_ai
   ```

4. **Database Setup**
   - Ensure MongoDB is running
   - The system will automatically create required collections

## 🚀 Usage

### Running the Recommendation System

```bash
python app.py
```

This will:
1. Load user profiles from MongoDB
2. Analyze chat history and user behavior
3. Generate personalized notification recommendations
4. Queue notifications for delivery via Redis

### Key Functions

#### User Context Creation
```python
from algo.bandit_algo import create_user_context

user_context = create_user_context(user_profile)
```

#### Getting Recommendations
```python
from algo.bandit_algo import NotificationRecommendationSystem

system = NotificationRecommendationSystem(notification_data)
recommendation = system.get_recommendation(user_context)
```

#### Recording User Interactions
```python
system.record_interaction(recommendation, clicked=True, user_context)
```

## 📊 Data Models

### User Profile Structure
```python
{
    "id": "partnr_12345",
    "name": "User Name",
    "interests": ["category1", "category2"],
    "gender": "male/female",
    "age": 25,
    "subscription_status": "free/premium/premium_plus"
}
```

### Notification Template Structure
```python
{
    "notification_id": "unique_id",
    "title": "Notification Title",
    "content": "Notification content with {placeholders}",
    "category": "engagement/social/monetization/retention",
    "type": "Message Received/New Match/etc",
    "nsfw_level": 0-3
}
```

## 🎯 Algorithm Details

### Multi-Armed Bandit (LinUCB)
- **Context Features**: 25-dimensional feature vector including user demographics, behavior, and preferences
- **Exploration vs Exploitation**: Balances trying new notifications vs. using proven ones
- **Diversity Factor**: Ensures variety in notification categories
- **Real-time Learning**: Updates model based on user interactions

### Feature Engineering
- User subscription status and spending behavior
- Chat activity and character preferences
- Temporal patterns (time of day, day of week)
- Notification preferences and tolerance levels
- Historical engagement metrics

## 📈 Performance Monitoring

The system tracks:
- Overall click-through rates (CTR)
- Category-specific performance
- Template effectiveness
- User engagement patterns
- Time-based performance metrics

Access performance reports via:
```python
report = system.get_performance_report()
```

## 🔧 Configuration

### Notification Categories
- `engagement`: General user engagement
- `social`: Social interactions and matches
- `monetization`: Premium features and offers
- `retention`: Re-engagement for inactive users
- `activity`: Real-time activity notifications
- `romantic`: Romantic content
- `nsfw`: Adult content (filtered by user tolerance)

### NSFW Levels
- `0`: Safe for all users
- `1`: Mild romantic content
- `2`: Suggestive content
- `3`: Explicit content

## 📁 Project Structure

```
foxy-ai-service-rs/
├── algo/
│   ├── __init__.py
│   └── bandit_algo.py          # Core bandit algorithm
├── utils/
│   ├── common.py               # Common constants and data
│   ├── mgdatabase.py           # MongoDB operations
│   ├── notification_queue.py   # Redis queue management
│   └── utils.py                # Utility functions
├── docs/
│   ├── mongodb_guide.md        # MongoDB setup guide
│   └── notification_queue_guide.md  # Queue setup guide
├── app.py                      # Main application
├── requirements.txt            # Python dependencies
└── readme.md                   # This file
```

## 🧪 Testing

Run the system with test data:
```bash
python -c "import asyncio; from app import create_notification_recommender_system; asyncio.run(create_notification_recommender_system())"
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For support and questions:
- Check the documentation in the `docs/` directory
- Review the code comments for implementation details
- Contact the development team

## 🔄 Version History

- **v1.0.0**: Initial release with basic bandit algorithm
- **Current**: Enhanced feature extraction and performance monitoring